import { Injectable, inject } from '@angular/core';
import { Observable, from, map, switchMap, of, combineLatest } from 'rxjs';
import { 
  Firestore, 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  query, 
  where, 
  getDocs 
} from '@angular/fire/firestore';

// Services
import { CalendarService } from '../../calendar/services/calendar.service';
import { TaskManagementService } from './task-management.service';

// Models
import { Task, Checklist, TaskCalendarEvent } from '../models/task.model';
import { CalendarEvent } from '../../calendar/models/calendar.model';

@Injectable({
  providedIn: 'root'
})
export class TaskCalendarIntegrationService {
  private firestore = inject(Firestore);
  private calendarService = inject(CalendarService);

  // Collections
  private taskCalendarEventsCollection = collection(this.firestore, 'taskCalendarEvents');

  constructor() {}

  // ==================== TASK-CALENDAR SYNC ====================

  /**
   * Sync a task with the calendar system
   */
  syncTaskWithCalendar(task: Task): Observable<string> {
    if (!task.syncWithCalendar || !task.dueDate) {
      return of('');
    }

    const calendarEvent: Omit<CalendarEvent, 'id'> = {
      title: `📋 ${task.title}`,
      description: this.buildTaskDescription(task),
      start: task.startDate || task.dueDate,
      end: task.dueDate,
      allDay: !this.hasTimeComponent(task.dueDate),
      type: 'other',
      status: this.mapTaskStatusToCalendarStatus(task.status),
      assignedStaff: task.assignedTo,
      createdBy: task.createdBy,
      businessId: task.businessId,
      location: task.location,
      color: this.getTaskColor(task.priority),
      backgroundColor: this.getTaskBackgroundColor(task.priority),
      borderColor: this.getTaskBorderColor(task.status),
      metadata: {
        taskId: task.id,
        taskType: 'task',
        priority: task.priority,
        category: task.category,
        progress: task.progress
      }
    };

    return this.calendarService.createEvent(calendarEvent).pipe(
      switchMap(calendarEventId => {
        // Store the sync relationship
        const syncData: Omit<TaskCalendarEvent, 'id'> = {
          taskId: task.id,
          calendarEventId,
          syncStatus: 'synced',
          lastSyncAt: new Date()
        };

        return from(addDoc(this.taskCalendarEventsCollection, syncData)).pipe(
          map(() => calendarEventId)
        );
      })
    );
  }

  /**
   * Sync a checklist with the calendar system
   */
  syncChecklistWithCalendar(checklist: Checklist): Observable<string> {
    if (!checklist.syncWithCalendar || !checklist.dueDate) {
      return of('');
    }

    const calendarEvent: Omit<CalendarEvent, 'id'> = {
      title: `✅ ${checklist.title}`,
      description: this.buildChecklistDescription(checklist),
      start: checklist.startDate || checklist.dueDate,
      end: checklist.dueDate,
      allDay: !this.hasTimeComponent(checklist.dueDate),
      type: 'other',
      status: this.mapTaskStatusToCalendarStatus(checklist.status),
      assignedStaff: checklist.assignedTo,
      createdBy: checklist.createdBy,
      businessId: checklist.businessId,
      location: checklist.location,
      color: this.getTaskColor(checklist.priority),
      backgroundColor: this.getTaskBackgroundColor(checklist.priority),
      borderColor: this.getTaskBorderColor(checklist.status),
      metadata: {
        taskId: checklist.id,
        taskType: 'checklist',
        priority: checklist.priority,
        category: checklist.category,
        completionPercentage: checklist.completionPercentage,
        itemCount: checklist.items?.length || 0
      }
    };

    return this.calendarService.createEvent(calendarEvent).pipe(
      switchMap(calendarEventId => {
        // Store the sync relationship
        const syncData: Omit<TaskCalendarEvent, 'id'> = {
          taskId: checklist.id,
          calendarEventId,
          syncStatus: 'synced',
          lastSyncAt: new Date()
        };

        return from(addDoc(this.taskCalendarEventsCollection, syncData)).pipe(
          map(() => calendarEventId)
        );
      })
    );
  }

  /**
   * Update calendar event when task changes
   */
  updateTaskCalendarSync(taskId: string, updates: Partial<Task | Checklist>): Observable<void> {
    return this.getTaskCalendarSync(taskId).pipe(
      switchMap(sync => {
        if (!sync) {
          return of(void 0);
        }

        // Get the current calendar event
        return this.calendarService.getEvent(sync.calendarEventId).pipe(
          switchMap(calendarEvent => {
            if (!calendarEvent) {
              return this.markSyncAsError(sync.taskId, 'Calendar event not found');
            }

            // Update calendar event with task changes
            const eventUpdates: Partial<CalendarEvent> = {};

            if (updates.title) {
              eventUpdates.title = updates.type === 'checklist' 
                ? `✅ ${updates.title}` 
                : `📋 ${updates.title}`;
            }

            if (updates.dueDate !== undefined) {
              eventUpdates.end = updates.dueDate;
              if (!updates.startDate && updates.dueDate) {
                eventUpdates.start = updates.dueDate;
              }
            }

            if (updates.startDate !== undefined) {
              eventUpdates.start = updates.startDate;
            }

            if (updates.status) {
              eventUpdates.status = this.mapTaskStatusToCalendarStatus(updates.status);
              eventUpdates.borderColor = this.getTaskBorderColor(updates.status);
            }

            if (updates.priority) {
              eventUpdates.color = this.getTaskColor(updates.priority);
              eventUpdates.backgroundColor = this.getTaskBackgroundColor(updates.priority);
            }

            if (updates.assignedTo) {
              eventUpdates.assignedStaff = updates.assignedTo;
            }

            if (updates.location !== undefined) {
              eventUpdates.location = updates.location;
            }

            // Update metadata
            if (eventUpdates.metadata || updates.progress !== undefined || updates.completionPercentage !== undefined) {
              eventUpdates.metadata = {
                ...calendarEvent.metadata,
                ...(updates.progress !== undefined && { progress: updates.progress }),
                ...(updates.completionPercentage !== undefined && { completionPercentage: updates.completionPercentage })
              };
            }

            return this.calendarService.updateEvent(sync.calendarEventId, eventUpdates).pipe(
              switchMap(() => this.updateSyncStatus(sync.taskId, 'synced'))
            );
          })
        );
      })
    );
  }

  /**
   * Remove calendar sync when task is deleted
   */
  removeTaskCalendarSync(taskId: string): Observable<void> {
    return this.getTaskCalendarSync(taskId).pipe(
      switchMap(sync => {
        if (!sync) {
          return of(void 0);
        }

        // Delete the calendar event
        return this.calendarService.deleteEvent(sync.calendarEventId).pipe(
          switchMap(() => {
            // Delete the sync record
            const syncDocRef = doc(this.taskCalendarEventsCollection, sync.taskId);
            return from(deleteDoc(syncDocRef));
          })
        );
      })
    );
  }

  /**
   * Get all tasks/checklists for a specific calendar date range
   */
  getTasksForCalendarPeriod(businessId: string, startDate: Date, endDate: Date): Observable<(Task | Checklist)[]> {
    // This would integrate with TaskManagementService to get tasks within date range
    // and return them formatted for calendar display
    return of([]);
  }

  /**
   * Bulk sync multiple tasks with calendar
   */
  bulkSyncTasksWithCalendar(tasks: (Task | Checklist)[]): Observable<string[]> {
    const syncOperations = tasks
      .filter(task => task.syncWithCalendar && task.dueDate)
      .map(task => 
        task.type === 'checklist' 
          ? this.syncChecklistWithCalendar(task as Checklist)
          : this.syncTaskWithCalendar(task as Task)
      );

    return combineLatest(syncOperations);
  }

  // ==================== PRIVATE HELPER METHODS ====================

  private getTaskCalendarSync(taskId: string): Observable<TaskCalendarEvent | null> {
    const q = query(this.taskCalendarEventsCollection, where('taskId', '==', taskId));
    
    return from(getDocs(q)).pipe(
      map(snapshot => {
        if (snapshot.empty) {
          return null;
        }
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as TaskCalendarEvent;
      })
    );
  }

  private updateSyncStatus(taskId: string, status: 'synced' | 'pending' | 'error', error?: string): Observable<void> {
    return this.getTaskCalendarSync(taskId).pipe(
      switchMap(sync => {
        if (!sync) {
          return of(void 0);
        }

        const syncDocRef = doc(this.taskCalendarEventsCollection, sync.id);
        const updates: Partial<TaskCalendarEvent> = {
          syncStatus: status,
          lastSyncAt: new Date(),
          ...(error && { syncError: error })
        };

        return from(updateDoc(syncDocRef, updates));
      })
    );
  }

  private markSyncAsError(taskId: string, error: string): Observable<void> {
    return this.updateSyncStatus(taskId, 'error', error);
  }

  private buildTaskDescription(task: Task): string {
    let description = task.description || '';
    
    description += `\n\n📊 Progress: ${task.progress}%`;
    description += `\n🎯 Priority: ${task.priority.toUpperCase()}`;
    description += `\n📂 Category: ${task.category}`;
    
    if (task.estimatedDuration) {
      description += `\n⏱️ Estimated: ${task.estimatedDuration} minutes`;
    }
    
    if (task.tags && task.tags.length > 0) {
      description += `\n🏷️ Tags: ${task.tags.join(', ')}`;
    }

    return description.trim();
  }

  private buildChecklistDescription(checklist: Checklist): string {
    let description = checklist.description || '';
    
    description += `\n\n✅ Completion: ${checklist.completionPercentage}%`;
    description += `\n📋 Items: ${checklist.items?.length || 0}`;
    description += `\n🎯 Priority: ${checklist.priority.toUpperCase()}`;
    description += `\n📂 Category: ${checklist.category}`;
    
    if (checklist.requireAllItems) {
      description += `\n⚠️ All items required for completion`;
    }

    return description.trim();
  }

  private mapTaskStatusToCalendarStatus(taskStatus: string): 'scheduled' | 'confirmed' | 'completed' | 'cancelled' {
    switch (taskStatus) {
      case 'pending':
        return 'scheduled';
      case 'in-progress':
        return 'confirmed';
      case 'completed':
        return 'completed';
      case 'cancelled':
        return 'cancelled';
      default:
        return 'scheduled';
    }
  }

  private getTaskColor(priority: string): string {
    switch (priority) {
      case 'critical':
        return '#d32f2f';
      case 'urgent':
        return '#f57c00';
      case 'high':
        return '#1976d2';
      case 'medium':
        return '#388e3c';
      case 'low':
        return '#616161';
      default:
        return '#1976d2';
    }
  }

  private getTaskBackgroundColor(priority: string): string {
    switch (priority) {
      case 'critical':
        return '#ffebee';
      case 'urgent':
        return '#fff3e0';
      case 'high':
        return '#e3f2fd';
      case 'medium':
        return '#e8f5e8';
      case 'low':
        return '#f5f5f5';
      default:
        return '#e3f2fd';
    }
  }

  private getTaskBorderColor(status: string): string {
    switch (status) {
      case 'completed':
        return '#4caf50';
      case 'in-progress':
        return '#2196f3';
      case 'overdue':
        return '#f44336';
      case 'cancelled':
        return '#9e9e9e';
      default:
        return '#ff9800';
    }
  }

  private hasTimeComponent(date: Date): boolean {
    return date.getHours() !== 0 || date.getMinutes() !== 0 || date.getSeconds() !== 0;
  }
}
