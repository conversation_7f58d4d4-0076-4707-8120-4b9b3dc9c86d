import { Injectable, inject } from '@angular/core';
import {
  Firestore,
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  onSnapshot,
  QueryConstraint
} from '@angular/fire/firestore';
import { Observable, from, map, switchMap, combineLatest, of, BehaviorSubject } from 'rxjs';
import { AuthService } from '../../../core/auth/auth.service';
import { CalendarService } from '../../calendar/services/calendar.service';
import { TaskCalendarIntegrationService } from './task-calendar-integration.service';
import {
  Task,
  Checklist,
  ChecklistItem,
  TaskFilter,
  TaskSortOptions,
  TaskAnalytics,
  TaskTemplate,
  TaskNotification,
  BulkTaskOperation,
  TaskCalendarEvent
} from '../models/task.model';
import { CalendarEvent } from '../../calendar/models/calendar.model';

@Injectable({
  providedIn: 'root'
})
export class TaskManagementService {
  private firestore = inject(Firestore);
  private authService = inject(AuthService);
  private calendarService = inject(CalendarService);
  private calendarIntegration = inject(TaskCalendarIntegrationService);

  // Collections
  private tasksCollection = collection(this.firestore, 'tasks');
  private checklistsCollection = collection(this.firestore, 'checklists');
  private templatesCollection = collection(this.firestore, 'taskTemplates');
  private notificationsCollection = collection(this.firestore, 'taskNotifications');

  // Real-time data streams
  private tasksSubject = new BehaviorSubject<Task[]>([]);
  private checklistsSubject = new BehaviorSubject<Checklist[]>([]);

  public tasks$ = this.tasksSubject.asObservable();
  public checklists$ = this.checklistsSubject.asObservable();

  constructor() {
    // Initialize real-time listeners
    this.initializeRealtimeListeners();
  }

  // ==================== TASK CRUD OPERATIONS ====================

  /**
   * Create a new task
   */
  createTask(taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Observable<string> {
    const task: Omit<Task, 'id'> = {
      ...taskData,
      createdAt: new Date(),
      updatedAt: new Date(),
      statusHistory: [{
        fromStatus: '',
        toStatus: taskData.status,
        changedBy: taskData.createdBy,
        changedAt: new Date()
      }]
    };

    return from(addDoc(this.tasksCollection, this.convertDatesToTimestamps(task))).pipe(
      map(docRef => docRef.id),
      switchMap(taskId => {
        // Sync with calendar if enabled
        if (task.syncWithCalendar && task.dueDate) {
          return this.syncTaskWithCalendar(taskId, task);
        }
        return of(taskId);
      })
    );
  }

  /**
   * Create a new checklist
   */
  createChecklist(checklistData: Omit<Checklist, 'id' | 'createdAt' | 'updatedAt'>): Observable<string> {
    const checklist: Omit<Checklist, 'id'> = {
      ...checklistData,
      type: 'checklist',
      completionPercentage: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      statusHistory: [{
        fromStatus: '',
        toStatus: checklistData.status,
        changedBy: checklistData.createdBy,
        changedAt: new Date()
      }]
    };

    return from(addDoc(this.checklistsCollection, this.convertDatesToTimestamps(checklist))).pipe(
      map(docRef => docRef.id),
      switchMap(checklistId => {
        // Sync with calendar if enabled
        if (checklist.syncWithCalendar && checklist.dueDate) {
          return this.syncChecklistWithCalendar(checklistId, checklist);
        }
        return of(checklistId);
      })
    );
  }

  /**
   * Update an existing task
   */
  updateTask(taskId: string, updates: Partial<Task>): Observable<void> {
    const taskRef = doc(this.tasksCollection, taskId);

    return from(getDoc(taskRef)).pipe(
      switchMap(docSnap => {
        if (!docSnap.exists()) {
          throw new Error('Task not found');
        }

        const currentTask = this.convertTimestampsToDates(docSnap.data()) as Task;
        const updatedTask = {
          ...updates,
          updatedAt: new Date(),
          lastModifiedBy: updates.lastModifiedBy || currentTask.lastModifiedBy
        };

        // Add status change to history if status changed
        if (updates.status && updates.status !== currentTask.status) {
          const statusChange = {
            fromStatus: currentTask.status,
            toStatus: updates.status,
            changedBy: updatedTask.lastModifiedBy,
            changedAt: new Date(),
            reason: updates.statusHistory?.[0]?.reason
          };

          updatedTask.statusHistory = [
            statusChange,
            ...(currentTask.statusHistory || [])
          ];
        }

        return from(updateDoc(taskRef, this.convertDatesToTimestamps(updatedTask)));
      }),
      switchMap(() => {
        // Update calendar event if synced
        if (updates.syncWithCalendar !== false) {
          return this.updateCalendarSync(taskId, updates);
        }
        return of(void 0);
      })
    );
  }

  /**
   * Update checklist item completion
   */
  updateChecklistItem(checklistId: string, itemId: string, updates: Partial<ChecklistItem>): Observable<void> {
    const checklistRef = doc(this.checklistsCollection, checklistId);

    return from(getDoc(checklistRef)).pipe(
      switchMap(docSnap => {
        if (!docSnap.exists()) {
          throw new Error('Checklist not found');
        }

        const checklist = this.convertTimestampsToDates(docSnap.data()) as Checklist;
        const itemIndex = checklist.items.findIndex(item => item.id === itemId);

        if (itemIndex === -1) {
          throw new Error('Checklist item not found');
        }

        // Update the specific item
        checklist.items[itemIndex] = {
          ...checklist.items[itemIndex],
          ...updates
        };

        // Recalculate completion percentage
        const completedItems = checklist.items.filter(item => item.isCompleted).length;
        const completionPercentage = (completedItems / checklist.items.length) * 100;

        // Update overall checklist status if needed
        let newStatus = checklist.status;
        if (completionPercentage === 100) {
          newStatus = 'completed';
        } else if (completionPercentage > 0 && checklist.status === 'pending') {
          newStatus = 'in-progress';
        }

        const updateData = {
          items: checklist.items,
          completionPercentage,
          status: newStatus,
          updatedAt: new Date()
        };

        return from(updateDoc(checklistRef, this.convertDatesToTimestamps(updateData)));
      })
    );
  }

  /**
   * Delete a task
   */
  deleteTask(taskId: string): Observable<void> {
    const taskRef = doc(this.tasksCollection, taskId);
    return from(deleteDoc(taskRef)).pipe(
      switchMap(() => {
        // Remove calendar sync if exists
        return this.removeCalendarSync(taskId);
      })
    );
  }

  /**
   * Delete a checklist
   */
  deleteChecklist(checklistId: string): Observable<void> {
    const checklistRef = doc(this.checklistsCollection, checklistId);
    return from(deleteDoc(checklistRef)).pipe(
      switchMap(() => {
        // Remove calendar sync if exists
        return this.removeCalendarSync(checklistId);
      })
    );
  }

  // ==================== QUERY AND FILTERING ====================

  /**
   * Get tasks with filtering and sorting
   */
  getTasks(filter?: TaskFilter, sort?: TaskSortOptions, pageSize?: number): Observable<Task[]> {
    const constraints: QueryConstraint[] = [];

    if (filter) {
      if (filter.businessId) {
        constraints.push(where('businessId', '==', filter.businessId));
      }
      if (filter.status && filter.status.length > 0) {
        constraints.push(where('status', 'in', filter.status));
      }
      if (filter.assignedTo && filter.assignedTo.length > 0) {
        constraints.push(where('assignedTo', 'array-contains-any', filter.assignedTo));
      }
      if (filter.priority && filter.priority.length > 0) {
        constraints.push(where('priority', 'in', filter.priority));
      }
      if (filter.category && filter.category.length > 0) {
        constraints.push(where('category', 'in', filter.category));
      }
    }

    if (sort) {
      constraints.push(orderBy(sort.field, sort.direction));
    } else {
      constraints.push(orderBy('createdAt', 'desc'));
    }

    if (pageSize) {
      constraints.push(limit(pageSize));
    }

    const q = query(this.tasksCollection, ...constraints);

    return from(getDocs(q)).pipe(
      map(snapshot =>
        snapshot.docs.map(doc => ({
          id: doc.id,
          ...this.convertTimestampsToDates(doc.data())
        } as Task))
      )
    );
  }

  /**
   * Get checklists with filtering and sorting
   */
  getChecklists(filter?: TaskFilter, sort?: TaskSortOptions): Observable<Checklist[]> {
    const constraints: QueryConstraint[] = [];

    if (filter) {
      if (filter.businessId) {
        constraints.push(where('businessId', '==', filter.businessId));
      }
      if (filter.status && filter.status.length > 0) {
        constraints.push(where('status', 'in', filter.status));
      }
      if (filter.assignedTo && filter.assignedTo.length > 0) {
        constraints.push(where('assignedTo', 'array-contains-any', filter.assignedTo));
      }
    }

    if (sort) {
      constraints.push(orderBy(sort.field, sort.direction));
    } else {
      constraints.push(orderBy('createdAt', 'desc'));
    }

    const q = query(this.checklistsCollection, ...constraints);

    return from(getDocs(q)).pipe(
      map(snapshot =>
        snapshot.docs.map(doc => ({
          id: doc.id,
          ...this.convertTimestampsToDates(doc.data())
        } as Checklist))
      )
    );
  }

  /**
   * Get tasks assigned to a specific staff member
   */
  getStaffTasks(staffId: string, includeCompleted: boolean = false): Observable<Task[]> {
    const constraints: QueryConstraint[] = [
      where('assignedTo', 'array-contains', staffId)
    ];

    if (!includeCompleted) {
      constraints.push(where('status', '!=', 'completed'));
    }

    constraints.push(orderBy('dueDate', 'asc'));

    const q = query(this.tasksCollection, ...constraints);

    return from(getDocs(q)).pipe(
      map(snapshot =>
        snapshot.docs.map(doc => ({
          id: doc.id,
          ...this.convertTimestampsToDates(doc.data())
        } as Task))
      )
    );
  }

  // ==================== UTILITY METHODS ====================

  private initializeRealtimeListeners(): void {
    // This would be implemented to listen for real-time updates
    // For now, we'll use the observable pattern with manual updates
  }

  private convertDatesToTimestamps(data: any): any {
    const converted = { ...data };

    // Convert Date objects to Firestore Timestamps
    Object.keys(converted).forEach(key => {
      if (converted[key] instanceof Date) {
        converted[key] = Timestamp.fromDate(converted[key]);
      } else if (Array.isArray(converted[key])) {
        converted[key] = converted[key].map((item: any) =>
          typeof item === 'object' && item !== null ? this.convertDatesToTimestamps(item) : item
        );
      } else if (typeof converted[key] === 'object' && converted[key] !== null) {
        converted[key] = this.convertDatesToTimestamps(converted[key]);
      }
    });

    return converted;
  }

  private convertTimestampsToDates(data: any): any {
    const converted = { ...data };

    // Convert Firestore Timestamps to Date objects
    Object.keys(converted).forEach(key => {
      if (converted[key] && typeof converted[key].toDate === 'function') {
        converted[key] = converted[key].toDate();
      } else if (Array.isArray(converted[key])) {
        converted[key] = converted[key].map((item: any) =>
          typeof item === 'object' && item !== null ? this.convertTimestampsToDates(item) : item
        );
      } else if (typeof converted[key] === 'object' && converted[key] !== null) {
        converted[key] = this.convertTimestampsToDates(converted[key]);
      }
    });

    return converted;
  }

  private syncTaskWithCalendar(taskId: string, task: Task): Observable<string> {
    return this.calendarIntegration.syncTaskWithCalendar({ ...task, id: taskId });
  }

  private syncChecklistWithCalendar(checklistId: string, checklist: Checklist): Observable<string> {
    return this.calendarIntegration.syncChecklistWithCalendar({ ...checklist, id: checklistId });
  }

  private updateCalendarSync(taskId: string, updates: Partial<Task>): Observable<void> {
    return this.calendarIntegration.updateTaskCalendarSync(taskId, updates);
  }

  private removeCalendarSync(taskId: string): Observable<void> {
    return this.calendarIntegration.removeTaskCalendarSync(taskId);
  }
}
