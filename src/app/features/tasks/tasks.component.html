<div class="tasks-container">
  <!-- Header Section -->
  <div class="tasks-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <mat-icon>assignment</mat-icon>
          Tasks & Checklists
        </h1>
        <p class="subtitle">Manage tasks, checklists, and team productivity with AI assistance</p>
      </div>

      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="createTask()">
          <mat-icon>add_task</mat-icon>
          Create Task
        </button>
        <button mat-raised-button color="accent" (click)="createChecklist()">
          <mat-icon>checklist</mat-icon>
          Create Checklist
        </button>
        <button mat-stroked-button color="primary" (click)="getAITaskSuggestions()">
          <mat-icon>psychology</mat-icon>
          AI Suggestions
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="filters-section">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search tasks and checklists</mat-label>
        <input matInput [(ngModel)]="searchText" placeholder="Search by title, description, or assignee">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Status</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let status of statusOptions" [value]="status.value">
            <mat-chip [color]="status.color">{{ status.label }}</mat-chip>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Priority</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let priority of priorityOptions" [value]="priority.value">
            <mat-chip [color]="priority.color">{{ priority.label }}</mat-chip>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Category</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let category of categoryOptions" [value]="category.value">
            {{ category.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <button mat-icon-button (click)="clearFilters()" matTooltip="Clear all filters">
        <mat-icon>clear</mat-icon>
      </button>
    </div>
  </div>

  <!-- Loading Indicator -->
  <mat-progress-bar *ngIf="isLoading()" mode="indeterminate" class="loading-bar"></mat-progress-bar>

  <!-- Main Content Tabs -->
  <mat-tab-group [(selectedIndex)]="selectedTab" class="tasks-tabs">

    <!-- Tasks Tab -->
    <mat-tab label="Tasks">
      <ng-template matTabContent>
        <div class="tab-content">

          <!-- Tasks Summary Cards -->
          <div class="summary-cards" *ngIf="tasks$ | async as tasks">
            <mat-card class="summary-card">
              <mat-card-content>
                <div class="summary-content">
                  <mat-icon class="summary-icon pending">pending_actions</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'pending').length }}</h3>
                    <p>Pending Tasks</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="summary-card">
              <mat-card-content>
                <div class="summary-content">
                  <mat-icon class="summary-icon in-progress">hourglass_empty</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'in-progress').length }}</h3>
                    <p>In Progress</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="summary-card">
              <mat-card-content>
                <div class="summary-content">
                  <mat-icon class="summary-icon completed">check_circle</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'completed').length }}</h3>
                    <p>Completed</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="summary-card">
              <mat-card-content>
                <div class="summary-content">
                  <mat-icon class="summary-icon overdue">warning</mat-icon>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'overdue').length }}</h3>
                    <p>Overdue</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Tasks Table -->
          <mat-card class="tasks-table-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>assignment</mat-icon>
                All Tasks
              </mat-card-title>
              <div class="table-actions">
                <button mat-icon-button [matMenuTriggerFor]="sortMenu" matTooltip="Sort options">
                  <mat-icon>sort</mat-icon>
                </button>
                <mat-menu #sortMenu="matMenu">
                  <button mat-menu-item (click)="applySorting({field: 'title', direction: 'asc'})">
                    <mat-icon>sort_by_alpha</mat-icon>
                    Title A-Z
                  </button>
                  <button mat-menu-item (click)="applySorting({field: 'dueDate', direction: 'asc'})">
                    <mat-icon>schedule</mat-icon>
                    Due Date
                  </button>
                  <button mat-menu-item (click)="applySorting({field: 'priority', direction: 'desc'})">
                    <mat-icon>priority_high</mat-icon>
                    Priority
                  </button>
                  <button mat-menu-item (click)="applySorting({field: 'createdAt', direction: 'desc'})">
                    <mat-icon>access_time</mat-icon>
                    Created Date
                  </button>
                </mat-menu>
              </div>
            </mat-card-header>

            <mat-card-content>
              <div class="table-container">
                <table mat-table [dataSource]="tasks$ | async" class="tasks-table">

                  <!-- Title Column -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Task</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="task-title-cell">
                        <h4>{{ task.title }}</h4>
                        <p *ngIf="task.description">{{ task.description | slice:0:100 }}{{ task.description.length > 100 ? '...' : '' }}</p>
                        <div class="task-tags" *ngIf="task.tags && task.tags.length > 0">
                          <mat-chip *ngFor="let tag of task.tags | slice:0:3" class="task-tag">{{ tag }}</mat-chip>
                          <span *ngIf="task.tags.length > 3" class="more-tags">+{{ task.tags.length - 3 }} more</span>
                        </div>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Status Column -->
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let task">
                      <mat-chip [color]="getStatusColor(task.status)">
                        {{ task.status | titlecase }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Priority Column -->
                  <ng-container matColumnDef="priority">
                    <th mat-header-cell *matHeaderCellDef>Priority</th>
                    <td mat-cell *matCellDef="let task">
                      <mat-chip [color]="getPriorityColor(task.priority)">
                        {{ task.priority | titlecase }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Assigned To Column -->
                  <ng-container matColumnDef="assignedTo">
                    <th mat-header-cell *matHeaderCellDef>Assigned To</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="assignees">
                        {{ formatStaffNames(task.assignedTo) | async }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Due Date Column -->
                  <ng-container matColumnDef="dueDate">
                    <th mat-header-cell *matHeaderCellDef>Due Date</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="due-date" [class.overdue]="task.dueDate && task.dueDate < (new Date()) && task.status !== 'completed'">
                        <mat-icon *ngIf="task.dueDate && task.dueDate < (new Date()) && task.status !== 'completed'">warning</mat-icon>
                        {{ task.dueDate | date:'mediumDate' || 'No due date' }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Progress Column -->
                  <ng-container matColumnDef="progress">
                    <th mat-header-cell *matHeaderCellDef>Progress</th>
                    <td mat-cell *matCellDef="let task">
                      <div class="progress-cell">
                        <mat-progress-bar [value]="task.progress" mode="determinate"></mat-progress-bar>
                        <span class="progress-text">{{ task.progress }}%</span>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let task">
                      <button mat-icon-button [matMenuTriggerFor]="taskMenu" matTooltip="Task actions">
                        <mat-icon>more_vert</mat-icon>
                      </button>
                      <mat-menu #taskMenu="matMenu">
                        <button mat-menu-item (click)="editTask(task)">
                          <mat-icon>edit</mat-icon>
                          Edit
                        </button>
                        <button mat-menu-item>
                          <mat-icon>visibility</mat-icon>
                          View Details
                        </button>
                        <button mat-menu-item>
                          <mat-icon>content_copy</mat-icon>
                          Duplicate
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item (click)="deleteTask(task)" class="delete-action">
                          <mat-icon>delete</mat-icon>
                          Delete
                        </button>
                      </mat-menu>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="taskColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: taskColumns;" class="task-row"></tr>
                </table>

                <!-- Empty State -->
                <div *ngIf="(tasks$ | async)?.length === 0" class="empty-state">
                  <mat-icon>assignment</mat-icon>
                  <h3>No tasks found</h3>
                  <p>Create your first task or adjust your filters to see results.</p>
                  <button mat-raised-button color="primary" (click)="createTask()">
                    <mat-icon>add</mat-icon>
                    Create Task
                  </button>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-template>
    </mat-tab>

    <!-- Checklists Tab -->
    <mat-tab label="Checklists">
      <ng-template matTabContent>
        <div class="tab-content">
          <!-- Checklists content will be similar to tasks but with checklist-specific features -->
          <mat-card class="checklists-table-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>checklist</mat-icon>
                All Checklists
              </mat-card-title>
            </mat-card-header>

            <mat-card-content>
              <div class="table-container">
                <table mat-table [dataSource]="checklists$ | async" class="checklists-table">

                  <!-- Similar columns to tasks but with completion percentage -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Checklist</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="checklist-title-cell">
                        <h4>{{ checklist.title }}</h4>
                        <p *ngIf="checklist.description">{{ checklist.description | slice:0:100 }}{{ checklist.description.length > 100 ? '...' : '' }}</p>
                        <div class="checklist-info">
                          <span class="item-count">{{ checklist.items?.length || 0 }} items</span>
                        </div>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let checklist">
                      <mat-chip [color]="getStatusColor(checklist.status)">
                        {{ checklist.status | titlecase }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="priority">
                    <th mat-header-cell *matHeaderCellDef>Priority</th>
                    <td mat-cell *matCellDef="let checklist">
                      <mat-chip [color]="getPriorityColor(checklist.priority)">
                        {{ checklist.priority | titlecase }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="assignedTo">
                    <th mat-header-cell *matHeaderCellDef>Assigned To</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="assignees">
                        {{ formatStaffNames(checklist.assignedTo) | async }}
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="dueDate">
                    <th mat-header-cell *matHeaderCellDef>Due Date</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="due-date" [class.overdue]="checklist.dueDate && checklist.dueDate < (new Date()) && checklist.status !== 'completed'">
                        <mat-icon *ngIf="checklist.dueDate && checklist.dueDate < (new Date()) && checklist.status !== 'completed'">warning</mat-icon>
                        {{ checklist.dueDate | date:'mediumDate' || 'No due date' }}
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="completion">
                    <th mat-header-cell *matHeaderCellDef>Completion</th>
                    <td mat-cell *matCellDef="let checklist">
                      <div class="completion-cell">
                        <mat-progress-bar [value]="checklist.completionPercentage" mode="determinate"></mat-progress-bar>
                        <span class="completion-text">{{ checklist.completionPercentage }}%</span>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let checklist">
                      <button mat-icon-button [matMenuTriggerFor]="checklistMenu" matTooltip="Checklist actions">
                        <mat-icon>more_vert</mat-icon>
                      </button>
                      <mat-menu #checklistMenu="matMenu">
                        <button mat-menu-item (click)="editChecklist(checklist)">
                          <mat-icon>edit</mat-icon>
                          Edit
                        </button>
                        <button mat-menu-item>
                          <mat-icon>visibility</mat-icon>
                          View Details
                        </button>
                        <button mat-menu-item>
                          <mat-icon>content_copy</mat-icon>
                          Duplicate
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item (click)="deleteChecklist(checklist)" class="delete-action">
                          <mat-icon>delete</mat-icon>
                          Delete
                        </button>
                      </mat-menu>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="checklistColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: checklistColumns;" class="checklist-row"></tr>
                </table>

                <!-- Empty State -->
                <div *ngIf="(checklists$ | async)?.length === 0" class="empty-state">
                  <mat-icon>checklist</mat-icon>
                  <h3>No checklists found</h3>
                  <p>Create your first checklist or adjust your filters to see results.</p>
                  <button mat-raised-button color="accent" (click)="createChecklist()">
                    <mat-icon>add</mat-icon>
                    Create Checklist
                  </button>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-template>
    </mat-tab>

    <!-- Analytics Tab -->
    <mat-tab label="Analytics">
      <ng-template matTabContent>
        <div class="tab-content">
          <div class="analytics-section">
            <mat-card class="analytics-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>analytics</mat-icon>
                  Productivity Analytics
                </mat-card-title>
                <div class="analytics-actions">
                  <button mat-raised-button color="primary" (click)="analyzeProductivity()">
                    <mat-icon>psychology</mat-icon>
                    AI Analysis
                  </button>
                </div>
              </mat-card-header>
              <mat-card-content>
                <p>AI-powered productivity insights and recommendations will be displayed here.</p>
                <p>Click "AI Analysis" to generate personalized productivity recommendations.</p>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </ng-template>
    </mat-tab>

  </mat-tab-group>
</div>
