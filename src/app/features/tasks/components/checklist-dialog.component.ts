import { Component, Inject, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { Observable, map } from 'rxjs';

// Angular Material
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';

// Services
import { TaskManagementService } from '../services/task-management.service';
import { AITaskAssistantService } from '../services/ai-task-assistant.service';
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { Checklist, ChecklistItem } from '../models/task.model';
import { StaffMember } from '../../staff/models/staff.model';

export interface ChecklistDialogData {
  checklist?: Checklist;
  businessId: string;
  mode: 'create' | 'edit';
}

@Component({
  selector: 'app-checklist-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DragDropModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatExpansionModule,
    MatDividerModule,
    MatListModule
  ],
  template: `
    <div class="checklist-dialog">
      <h2 mat-dialog-title>
        <mat-icon>{{ data.mode === 'create' ? 'checklist' : 'edit' }}</mat-icon>
        {{ data.mode === 'create' ? 'Create New Checklist' : 'Edit Checklist' }}
      </h2>

      <mat-dialog-content>
        <form [formGroup]="checklistForm" class="checklist-form">
          
          <!-- Basic Information -->
          <div class="form-section">
            <h3>Basic Information</h3>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Checklist Title</mat-label>
              <input matInput formControlName="title" placeholder="Enter checklist title" required>
              <mat-error *ngIf="checklistForm.get('title')?.hasError('required')">
                Checklist title is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="3" 
                       placeholder="Describe the checklist purpose and context"></textarea>
            </mat-form-field>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Priority</mat-label>
                <mat-select formControlName="priority" required>
                  <mat-option value="low">Low</mat-option>
                  <mat-option value="medium">Medium</mat-option>
                  <mat-option value="high">High</mat-option>
                  <mat-option value="urgent">Urgent</mat-option>
                  <mat-option value="critical">Critical</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Category</mat-label>
                <mat-select formControlName="category" required>
                  <mat-option value="administrative">Administrative</mat-option>
                  <mat-option value="customer-service">Customer Service</mat-option>
                  <mat-option value="training">Training</mat-option>
                  <mat-option value="maintenance">Maintenance</mat-option>
                  <mat-option value="project">Project</mat-option>
                  <mat-option value="safety">Safety</mat-option>
                  <mat-option value="compliance">Compliance</mat-option>
                  <mat-option value="custom">Custom</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status" required>
                  <mat-option value="pending">Pending</mat-option>
                  <mat-option value="in-progress">In Progress</mat-option>
                  <mat-option value="completed">Completed</mat-option>
                  <mat-option value="on-hold">On Hold</mat-option>
                  <mat-option value="cancelled">Cancelled</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <!-- Assignment and Scheduling -->
          <div class="form-section">
            <h3>Assignment & Scheduling</h3>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Assign to Staff</mat-label>
              <mat-select formControlName="assignedTo" multiple required>
                <mat-option *ngFor="let staff of staff$ | async" [value]="staff.id">
                  {{ staff.firstName }} {{ staff.lastName }} - {{ staff.position }}
                </mat-option>
              </mat-select>
              <mat-hint>Select one or more staff members</mat-hint>
            </mat-form-field>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Start Date</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Due Date</mat-label>
                <input matInput [matDatepicker]="duePicker" formControlName="dueDate">
                <mat-datepicker-toggle matIconSuffix [for]="duePicker"></mat-datepicker-toggle>
                <mat-datepicker #duePicker></mat-datepicker>
              </mat-form-field>
            </div>
          </div>

          <!-- Checklist Settings -->
          <div class="form-section">
            <h3>Checklist Settings</h3>
            
            <mat-checkbox formControlName="allowPartialCompletion">
              Allow partial completion (checklist can be marked complete with some items pending)
            </mat-checkbox>

            <mat-checkbox formControlName="requireAllItems">
              Require all items to be completed
            </mat-checkbox>

            <mat-checkbox formControlName="verificationRequired">
              Require manager verification for completion
            </mat-checkbox>

            <mat-checkbox formControlName="syncWithCalendar">
              Sync with calendar
            </mat-checkbox>
          </div>

          <!-- Checklist Items -->
          <div class="form-section">
            <div class="items-header">
              <h3>Checklist Items</h3>
              <div class="items-actions">
                <button type="button" mat-stroked-button color="primary" 
                        (click)="generateAIItems()" [disabled]="isLoadingAI()">
                  <mat-icon>psychology</mat-icon>
                  AI Generate
                </button>
                <button type="button" mat-raised-button color="accent" (click)="addItem()">
                  <mat-icon>add</mat-icon>
                  Add Item
                </button>
              </div>
            </div>

            <div class="checklist-items" cdkDropList (cdkDropListDropped)="dropItem($event)">
              <div *ngFor="let item of items.controls; let i = index" 
                   class="checklist-item" 
                   cdkDrag>
                <div class="item-content" [formGroup]="item">
                  <div class="item-header">
                    <mat-icon cdkDragHandle class="drag-handle">drag_indicator</mat-icon>
                    <span class="item-number">{{ i + 1 }}</span>
                    <button type="button" mat-icon-button color="warn" 
                            (click)="removeItem(i)" 
                            matTooltip="Remove item">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>

                  <div class="item-form">
                    <mat-form-field appearance="outline" class="item-title">
                      <mat-label>Item Title</mat-label>
                      <input matInput formControlName="title" placeholder="Enter checklist item" required>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="item-description">
                      <mat-label>Description (optional)</mat-label>
                      <textarea matInput formControlName="description" rows="2" 
                               placeholder="Additional details or instructions"></textarea>
                    </mat-form-field>

                    <div class="item-options">
                      <mat-checkbox formControlName="isRequired">Required</mat-checkbox>
                      <mat-checkbox formControlName="requiresVerification">Requires Verification</mat-checkbox>
                      <mat-checkbox formControlName="photoRequired">Photo Required</mat-checkbox>
                      <mat-checkbox formControlName="signatureRequired">Signature Required</mat-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div *ngIf="items.length === 0" class="empty-items">
              <mat-icon>checklist</mat-icon>
              <p>No checklist items yet. Add items manually or use AI to generate them.</p>
            </div>
          </div>

          <!-- Advanced Options -->
          <mat-expansion-panel class="advanced-options">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon>settings</mat-icon>
                Advanced Options
              </mat-panel-title>
            </mat-expansion-panel-header>

            <div class="form-section">
              <mat-checkbox formControlName="isTemplate">
                Save as template for future use
              </mat-checkbox>

              <mat-form-field appearance="outline" class="full-width" 
                             *ngIf="checklistForm.get('isTemplate')?.value">
                <mat-label>Template Category</mat-label>
                <input matInput formControlName="templateCategory" 
                       placeholder="e.g., Opening Procedures, Safety Checks">
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Location</mat-label>
                <input matInput formControlName="location" placeholder="Checklist location (optional)">
              </mat-form-field>
            </div>
          </mat-expansion-panel>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()">Cancel</button>
        <button mat-raised-button color="primary" 
                (click)="onSave()" 
                [disabled]="checklistForm.invalid || isSaving() || items.length === 0">
          <mat-icon>{{ data.mode === 'create' ? 'add' : 'save' }}</mat-icon>
          {{ data.mode === 'create' ? 'Create Checklist' : 'Save Changes' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .checklist-dialog {
      width: 100%;
      max-width: 900px;

      h2 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;

        mat-icon {
          color: var(--mdc-theme-primary);
        }
      }

      .checklist-form {
        .form-section {
          margin-bottom: 24px;

          h3 {
            margin: 0 0 16px 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--mdc-theme-on-surface);
          }

          .full-width {
            width: 100%;
          }

          .form-row {
            display: flex;
            gap: 16px;
            align-items: flex-start;

            mat-form-field {
              flex: 1;
            }
          }

          mat-checkbox {
            margin-bottom: 8px;
            display: block;
          }

          .items-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .items-actions {
              display: flex;
              gap: 8px;
            }
          }

          .checklist-items {
            .checklist-item {
              margin-bottom: 16px;
              border: 1px solid var(--mdc-theme-outline-variant);
              border-radius: 8px;
              background: var(--mdc-theme-surface);

              &.cdk-drag-preview {
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
              }

              .item-content {
                padding: 16px;

                .item-header {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  margin-bottom: 12px;

                  .drag-handle {
                    cursor: move;
                    color: var(--mdc-theme-on-surface-variant);
                  }

                  .item-number {
                    font-weight: 500;
                    color: var(--mdc-theme-primary);
                    min-width: 20px;
                  }

                  button {
                    margin-left: auto;
                  }
                }

                .item-form {
                  .item-title {
                    width: 100%;
                    margin-bottom: 8px;
                  }

                  .item-description {
                    width: 100%;
                    margin-bottom: 12px;
                  }

                  .item-options {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 16px;

                    mat-checkbox {
                      margin-bottom: 0;
                    }
                  }
                }
              }
            }
          }

          .empty-items {
            text-align: center;
            padding: 32px;
            color: var(--mdc-theme-on-surface-variant);
            border: 2px dashed var(--mdc-theme-outline-variant);
            border-radius: 8px;

            mat-icon {
              font-size: 3rem;
              width: 3rem;
              height: 3rem;
              margin-bottom: 16px;
              opacity: 0.5;
            }

            p {
              margin: 0;
            }
          }
        }

        .advanced-options {
          margin-bottom: 16px;

          mat-expansion-panel-header {
            mat-panel-title {
              display: flex;
              align-items: center;
              gap: 8px;

              mat-icon {
                color: var(--mdc-theme-primary);
              }
            }
          }
        }
      }
    }

    @media (max-width: 600px) {
      .checklist-dialog {
        .checklist-form {
          .form-section {
            .form-row {
              flex-direction: column;
              gap: 8px;
            }

            .items-header {
              flex-direction: column;
              align-items: stretch;
              gap: 12px;

              .items-actions {
                justify-content: center;
              }
            }

            .checklist-items {
              .checklist-item {
                .item-content {
                  .item-options {
                    flex-direction: column;
                    gap: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  `]
})
export class ChecklistDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);

  // Signals
  isLoadingAI = signal(false);
  isSaving = signal(false);

  // Form
  checklistForm!: FormGroup;

  // Data
  staff$!: Observable<StaffMember[]>;

  constructor(
    public dialogRef: MatDialogRef<ChecklistDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ChecklistDialogData
  ) {}

  get items(): FormArray {
    return this.checklistForm.get('items') as FormArray;
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadStaff();
    
    if (this.data.checklist) {
      this.populateForm(this.data.checklist);
    }
  }

  private initializeForm(): void {
    this.checklistForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      priority: ['medium', Validators.required],
      category: ['administrative', Validators.required],
      status: ['pending', Validators.required],
      assignedTo: [[], Validators.required],
      startDate: [null],
      dueDate: [null],
      allowPartialCompletion: [false],
      requireAllItems: [true],
      verificationRequired: [false],
      syncWithCalendar: [true],
      isTemplate: [false],
      templateCategory: [''],
      location: [''],
      items: this.fb.array([])
    });
  }

  private loadStaff(): void {
    this.staff$ = this.staffService.getActiveStaff(this.data.businessId);
  }

  private populateForm(checklist: Checklist): void {
    this.checklistForm.patchValue({
      title: checklist.title,
      description: checklist.description,
      priority: checklist.priority,
      category: checklist.category,
      status: checklist.status,
      assignedTo: checklist.assignedTo,
      startDate: checklist.startDate,
      dueDate: checklist.dueDate,
      allowPartialCompletion: checklist.allowPartialCompletion,
      requireAllItems: checklist.requireAllItems,
      verificationRequired: checklist.verificationRequired,
      syncWithCalendar: checklist.syncWithCalendar,
      isTemplate: checklist.isTemplate,
      templateCategory: checklist.templateCategory,
      location: checklist.location
    });

    // Populate items
    checklist.items.forEach(item => {
      this.items.push(this.createItemFormGroup(item));
    });
  }

  private createItemFormGroup(item?: ChecklistItem): FormGroup {
    return this.fb.group({
      id: [item?.id || `item-${Date.now()}-${Math.random()}`],
      title: [item?.title || '', Validators.required],
      description: [item?.description || ''],
      isRequired: [item?.isRequired ?? true],
      requiresVerification: [item?.requiresVerification ?? false],
      photoRequired: [item?.photoRequired ?? false],
      signatureRequired: [item?.signatureRequired ?? false],
      order: [item?.order || this.items.length + 1]
    });
  }

  addItem(): void {
    this.items.push(this.createItemFormGroup());
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
    this.updateItemOrders();
  }

  dropItem(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.items.controls, event.previousIndex, event.currentIndex);
    this.updateItemOrders();
  }

  private updateItemOrders(): void {
    this.items.controls.forEach((control, index) => {
      control.get('order')?.setValue(index + 1);
    });
  }

  async generateAIItems(): Promise<void> {
    this.isLoadingAI.set(true);
    
    try {
      const category = this.checklistForm.get('category')?.value;
      const title = this.checklistForm.get('title')?.value;
      const description = this.checklistForm.get('description')?.value;
      
      const context = `${title} - ${description}`;
      const aiItems = await this.aiService.generateChecklistTemplate(category, context);
      
      if (aiItems.length > 0) {
        // Clear existing items and add AI generated ones
        while (this.items.length > 0) {
          this.items.removeAt(0);
        }
        
        aiItems.forEach(item => {
          this.items.push(this.createItemFormGroup(item));
        });
        
        this.snackBar.open(`Generated ${aiItems.length} checklist items with AI`, 'Close', { duration: 5000 });
      }
    } catch (error) {
      this.snackBar.open('Error generating AI checklist items', 'Close', { duration: 3000 });
      console.error('AI checklist generation error:', error);
    } finally {
      this.isLoadingAI.set(false);
    }
  }

  onSave(): void {
    if (this.checklistForm.valid && this.items.length > 0) {
      this.isSaving.set(true);
      
      const formValue = this.checklistForm.value;
      const currentUser = this.authService.userProfile$.pipe(map(u => u)).toPromise();
      
      currentUser.then(user => {
        if (!user) return;

        const checklistData: Omit<Checklist, 'id' | 'createdAt' | 'updatedAt'> = {
          ...formValue,
          type: 'checklist' as const,
          businessId: this.data.businessId,
          createdBy: user.uid,
          lastModifiedBy: user.uid,
          items: formValue.items.map((item: any, index: number) => ({
            ...item,
            order: index + 1,
            isCompleted: false,
            aiGenerated: false
          })),
          completionPercentage: 0,
          progress: 0
        };

        const operation = this.data.mode === 'create' 
          ? this.taskService.createChecklist(checklistData)
          : this.taskService.updateTask(this.data.checklist!.id, checklistData);

        operation.subscribe({
          next: () => {
            this.snackBar.open(
              `Checklist ${this.data.mode === 'create' ? 'created' : 'updated'} successfully`, 
              'Close', 
              { duration: 3000 }
            );
            this.dialogRef.close(true);
          },
          error: (error) => {
            this.snackBar.open(
              `Error ${this.data.mode === 'create' ? 'creating' : 'updating'} checklist`, 
              'Close', 
              { duration: 3000 }
            );
            console.error('Checklist save error:', error);
            this.isSaving.set(false);
          }
        });
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
