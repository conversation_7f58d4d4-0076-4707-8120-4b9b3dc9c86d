import { Component, Inject, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Observable, map, startWith } from 'rxjs';

// Angular Material
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSliderModule } from '@angular/material/slider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';

// Services
import { TaskManagementService } from '../services/task-management.service';
import { AITaskAssistantService } from '../services/ai-task-assistant.service';
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { Task, RecurrencePattern, CompletionRequirement } from '../models/task.model';
import { StaffMember } from '../../staff/models/staff.model';

export interface TaskDialogData {
  task?: Task;
  businessId: string;
  mode: 'create' | 'edit';
}

@Component({
  selector: 'app-task-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatAutocompleteModule,
    MatCheckboxModule,
    MatSliderModule,
    MatExpansionModule,
    MatDividerModule
  ],
  template: `
    <div class="task-dialog">
      <h2 mat-dialog-title>
        <mat-icon>{{ data.mode === 'create' ? 'add_task' : 'edit' }}</mat-icon>
        {{ data.mode === 'create' ? 'Create New Task' : 'Edit Task' }}
      </h2>

      <mat-dialog-content>
        <form [formGroup]="taskForm" class="task-form">
          
          <!-- Basic Information -->
          <div class="form-section">
            <h3>Basic Information</h3>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Task Title</mat-label>
              <input matInput formControlName="title" placeholder="Enter task title" required>
              <mat-error *ngIf="taskForm.get('title')?.hasError('required')">
                Task title is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="3" 
                       placeholder="Describe the task in detail"></textarea>
            </mat-form-field>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Priority</mat-label>
                <mat-select formControlName="priority" required>
                  <mat-option value="low">Low</mat-option>
                  <mat-option value="medium">Medium</mat-option>
                  <mat-option value="high">High</mat-option>
                  <mat-option value="urgent">Urgent</mat-option>
                  <mat-option value="critical">Critical</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Category</mat-label>
                <mat-select formControlName="category" required>
                  <mat-option value="administrative">Administrative</mat-option>
                  <mat-option value="customer-service">Customer Service</mat-option>
                  <mat-option value="training">Training</mat-option>
                  <mat-option value="maintenance">Maintenance</mat-option>
                  <mat-option value="project">Project</mat-option>
                  <mat-option value="safety">Safety</mat-option>
                  <mat-option value="compliance">Compliance</mat-option>
                  <mat-option value="custom">Custom</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status" required>
                  <mat-option value="pending">Pending</mat-option>
                  <mat-option value="in-progress">In Progress</mat-option>
                  <mat-option value="completed">Completed</mat-option>
                  <mat-option value="on-hold">On Hold</mat-option>
                  <mat-option value="cancelled">Cancelled</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <!-- Assignment and Scheduling -->
          <div class="form-section">
            <h3>Assignment & Scheduling</h3>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Assign to Staff</mat-label>
              <mat-select formControlName="assignedTo" multiple required>
                <mat-option *ngFor="let staff of staff$ | async" [value]="staff.id">
                  {{ staff.firstName }} {{ staff.lastName }} - {{ staff.position }}
                </mat-option>
              </mat-select>
              <mat-hint>Select one or more staff members</mat-hint>
            </mat-form-field>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Start Date</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Due Date</mat-label>
                <input matInput [matDatepicker]="duePicker" formControlName="dueDate">
                <mat-datepicker-toggle matIconSuffix [for]="duePicker"></mat-datepicker-toggle>
                <mat-datepicker #duePicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Estimated Duration (minutes)</mat-label>
                <input matInput type="number" formControlName="estimatedDuration" 
                       placeholder="e.g., 60">
              </mat-form-field>
            </div>
          </div>

          <!-- Progress and Completion -->
          <div class="form-section">
            <h3>Progress & Completion</h3>
            
            <div class="progress-section">
              <label>Progress: {{ taskForm.get('progress')?.value || 0 }}%</label>
              <mat-slider min="0" max="100" step="5" discrete>
                <input matSliderThumb formControlName="progress">
              </mat-slider>
            </div>

            <mat-checkbox formControlName="verificationRequired">
              Require manager verification for completion
            </mat-checkbox>

            <mat-checkbox formControlName="syncWithCalendar">
              Sync with calendar
            </mat-checkbox>
          </div>

          <!-- Advanced Options -->
          <mat-expansion-panel class="advanced-options">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon>settings</mat-icon>
                Advanced Options
              </mat-panel-title>
            </mat-expansion-panel-header>

            <!-- Tags -->
            <div class="form-section">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Tags</mat-label>
                <mat-chip-grid #chipGrid>
                  <mat-chip-row *ngFor="let tag of tags()" (removed)="removeTag(tag)">
                    {{ tag }}
                    <button matChipRemove>
                      <mat-icon>cancel</mat-icon>
                    </button>
                  </mat-chip-row>
                </mat-chip-grid>
                <input placeholder="Add tags..." 
                       [matChipInputFor]="chipGrid"
                       (matChipInputTokenEnd)="addTag($event)">
              </mat-form-field>
            </div>

            <!-- Recurrence -->
            <div class="form-section">
              <mat-checkbox formControlName="isRecurring">
                Make this a recurring task
              </mat-checkbox>

              <div *ngIf="taskForm.get('isRecurring')?.value" class="recurrence-options">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Frequency</mat-label>
                    <mat-select formControlName="recurrenceFrequency">
                      <mat-option value="daily">Daily</mat-option>
                      <mat-option value="weekly">Weekly</mat-option>
                      <mat-option value="monthly">Monthly</mat-option>
                      <mat-option value="quarterly">Quarterly</mat-option>
                      <mat-option value="yearly">Yearly</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Interval</mat-label>
                    <input matInput type="number" formControlName="recurrenceInterval" 
                           placeholder="e.g., 2" min="1">
                    <mat-hint>Every X {{ taskForm.get('recurrenceFrequency')?.value || 'periods' }}</mat-hint>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>End Date</mat-label>
                    <input matInput [matDatepicker]="recurrenceEndPicker" formControlName="recurrenceEndDate">
                    <mat-datepicker-toggle matIconSuffix [for]="recurrenceEndPicker"></mat-datepicker-toggle>
                    <mat-datepicker #recurrenceEndPicker></mat-datepicker>
                  </mat-form-field>
                </div>
              </div>
            </div>

            <!-- Location -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Location</mat-label>
              <input matInput formControlName="location" placeholder="Task location (optional)">
            </mat-form-field>
          </mat-expansion-panel>

          <!-- AI Assistance -->
          <div class="ai-assistance-section" *ngIf="data.mode === 'create'">
            <mat-divider></mat-divider>
            <div class="ai-actions">
              <h3>
                <mat-icon>psychology</mat-icon>
                AI Assistance
              </h3>
              <button type="button" mat-stroked-button color="primary" 
                      (click)="getAISuggestions()" [disabled]="isLoadingAI()">
                <mat-icon>auto_awesome</mat-icon>
                Get AI Suggestions
              </button>
            </div>
          </div>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()">Cancel</button>
        <button mat-raised-button color="primary" 
                (click)="onSave()" 
                [disabled]="taskForm.invalid || isSaving()">
          <mat-icon>{{ data.mode === 'create' ? 'add' : 'save' }}</mat-icon>
          {{ data.mode === 'create' ? 'Create Task' : 'Save Changes' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .task-dialog {
      width: 100%;
      max-width: 800px;

      h2 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;

        mat-icon {
          color: var(--mdc-theme-primary);
        }
      }

      .task-form {
        .form-section {
          margin-bottom: 24px;

          h3 {
            margin: 0 0 16px 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--mdc-theme-on-surface);
          }

          .full-width {
            width: 100%;
          }

          .form-row {
            display: flex;
            gap: 16px;
            align-items: flex-start;

            mat-form-field {
              flex: 1;
            }
          }

          .progress-section {
            margin-bottom: 16px;

            label {
              display: block;
              margin-bottom: 8px;
              font-weight: 500;
              color: var(--mdc-theme-on-surface);
            }

            mat-slider {
              width: 100%;
            }
          }

          mat-checkbox {
            margin-bottom: 8px;
          }

          .recurrence-options {
            margin-top: 16px;
            padding: 16px;
            background: var(--mdc-theme-surface-variant);
            border-radius: 8px;
          }
        }

        .advanced-options {
          margin-bottom: 16px;

          mat-expansion-panel-header {
            mat-panel-title {
              display: flex;
              align-items: center;
              gap: 8px;

              mat-icon {
                color: var(--mdc-theme-primary);
              }
            }
          }
        }

        .ai-assistance-section {
          margin-top: 24px;
          padding-top: 16px;

          .ai-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;

            h3 {
              display: flex;
              align-items: center;
              gap: 8px;
              margin: 0;

              mat-icon {
                color: var(--mdc-theme-primary);
              }
            }
          }
        }
      }
    }

    @media (max-width: 600px) {
      .task-dialog {
        .task-form {
          .form-section {
            .form-row {
              flex-direction: column;
              gap: 8px;
            }
          }

          .ai-assistance-section {
            .ai-actions {
              flex-direction: column;
              align-items: stretch;
              gap: 16px;
            }
          }
        }
      }
    }
  `]
})
export class TaskDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);

  // Signals
  tags = signal<string[]>([]);
  isLoadingAI = signal(false);
  isSaving = signal(false);

  // Form
  taskForm!: FormGroup;

  // Data
  staff$!: Observable<StaffMember[]>;

  constructor(
    public dialogRef: MatDialogRef<TaskDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TaskDialogData
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadStaff();
    
    if (this.data.task) {
      this.populateForm(this.data.task);
    }
  }

  private initializeForm(): void {
    this.taskForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      priority: ['medium', Validators.required],
      category: ['administrative', Validators.required],
      status: ['pending', Validators.required],
      assignedTo: [[], Validators.required],
      startDate: [null],
      dueDate: [null],
      estimatedDuration: [null, [Validators.min(1)]],
      progress: [0, [Validators.min(0), Validators.max(100)]],
      verificationRequired: [false],
      syncWithCalendar: [true],
      isRecurring: [false],
      recurrenceFrequency: ['weekly'],
      recurrenceInterval: [1, [Validators.min(1)]],
      recurrenceEndDate: [null],
      location: ['']
    });
  }

  private loadStaff(): void {
    this.staff$ = this.staffService.getActiveStaff(this.data.businessId);
  }

  private populateForm(task: Task): void {
    this.taskForm.patchValue({
      title: task.title,
      description: task.description,
      priority: task.priority,
      category: task.category,
      status: task.status,
      assignedTo: task.assignedTo,
      startDate: task.startDate,
      dueDate: task.dueDate,
      estimatedDuration: task.estimatedDuration,
      progress: task.progress,
      verificationRequired: task.verificationRequired,
      syncWithCalendar: task.syncWithCalendar,
      isRecurring: task.isRecurring,
      location: task.location
    });

    if (task.tags) {
      this.tags.set([...task.tags]);
    }

    if (task.recurrencePattern) {
      this.taskForm.patchValue({
        recurrenceFrequency: task.recurrencePattern.frequency,
        recurrenceInterval: task.recurrencePattern.interval,
        recurrenceEndDate: task.recurrencePattern.endDate
      });
    }
  }

  addTag(event: any): void {
    const value = (event.value || '').trim();
    if (value && !this.tags().includes(value)) {
      this.tags.update(tags => [...tags, value]);
    }
    event.chipInput!.clear();
  }

  removeTag(tag: string): void {
    this.tags.update(tags => tags.filter(t => t !== tag));
  }

  async getAISuggestions(): Promise<void> {
    this.isLoadingAI.set(true);
    
    try {
      const currentUser = await this.authService.userProfile$.pipe(map(u => u)).toPromise();
      if (currentUser?.staffId) {
        const context = `Creating a ${this.taskForm.get('category')?.value} task with ${this.taskForm.get('priority')?.value} priority`;
        const suggestions = await this.aiService.generateTaskSuggestions(
          currentUser.staffId,
          this.data.businessId,
          context
        );
        
        if (suggestions.length > 0) {
          // Apply first suggestion to form
          const suggestion = suggestions[0];
          if (suggestion.suggestedData) {
            this.taskForm.patchValue(suggestion.suggestedData);
          }
          
          this.snackBar.open(`Applied AI suggestion: ${suggestion.title}`, 'Close', { duration: 5000 });
        }
      }
    } catch (error) {
      this.snackBar.open('Error getting AI suggestions', 'Close', { duration: 3000 });
      console.error('AI suggestions error:', error);
    } finally {
      this.isLoadingAI.set(false);
    }
  }

  onSave(): void {
    if (this.taskForm.valid) {
      this.isSaving.set(true);
      
      const formValue = this.taskForm.value;
      const currentUser = this.authService.userProfile$.pipe(map(u => u)).toPromise();
      
      currentUser.then(user => {
        if (!user) return;

        const taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'> = {
          ...formValue,
          type: 'task' as const,
          businessId: this.data.businessId,
          createdBy: user.uid,
          lastModifiedBy: user.uid,
          tags: this.tags(),
          recurrencePattern: formValue.isRecurring ? {
            frequency: formValue.recurrenceFrequency,
            interval: formValue.recurrenceInterval,
            endDate: formValue.recurrenceEndDate
          } : undefined
        };

        const operation = this.data.mode === 'create' 
          ? this.taskService.createTask(taskData)
          : this.taskService.updateTask(this.data.task!.id, taskData);

        operation.subscribe({
          next: () => {
            this.snackBar.open(
              `Task ${this.data.mode === 'create' ? 'created' : 'updated'} successfully`, 
              'Close', 
              { duration: 3000 }
            );
            this.dialogRef.close(true);
          },
          error: (error) => {
            this.snackBar.open(
              `Error ${this.data.mode === 'create' ? 'creating' : 'updating'} task`, 
              'Close', 
              { duration: 3000 }
            );
            console.error('Task save error:', error);
            this.isSaving.set(false);
          }
        });
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
